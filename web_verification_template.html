<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Discord Server Verification</title>
    <script src="https://js.hcaptcha.com/1/api.js" async defer></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 100%;
            text-align: center;
        }
        
        .logo {
            font-size: 48px;
            margin-bottom: 20px;
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }
        
        .verification-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: left;
        }
        
        .info-item {
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .info-label {
            font-weight: bold;
            color: #333;
        }
        
        .info-value {
            color: #666;
            font-family: monospace;
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
        }
        
        .hcaptcha-container {
            margin: 30px 0;
            display: flex;
            justify-content: center;
        }
        
        .submit-btn {
            background: #5865f2;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
            width: 100%;
        }
        
        .submit-btn:hover {
            background: #4752c4;
        }
        
        .submit-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .response-token {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            text-align: left;
        }
        
        .copy-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            margin-top: 10px;
        }
        
        .copy-btn:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🔐</div>
        <h1>Discord Server Verification</h1>
        <p class="subtitle">Complete the captcha below to verify your account</p>
        
        <div class="verification-info">
            <div class="info-item">
                <span class="info-label">User ID:</span>
                <span class="info-value" id="userId">Loading...</span>
            </div>
            <div class="info-item">
                <span class="info-label">Guild ID:</span>
                <span class="info-value" id="guildId">Loading...</span>
            </div>
            <div class="info-item">
                <span class="info-label">Token:</span>
                <span class="info-value" id="token">Loading...</span>
            </div>
        </div>
        
        <div class="hcaptcha-container">
            <div class="h-captcha" data-sitekey="YOUR_HCAPTCHA_SITE_KEY" data-callback="onCaptchaSuccess"></div>
        </div>
        
        <button class="submit-btn" id="submitBtn" onclick="submitVerification()" disabled>
            Complete Verification
        </button>
        
        <div id="status"></div>
        
        <div id="responseToken" class="response-token" style="display: none;">
            <strong>hCaptcha Response Token:</strong><br>
            <span id="tokenValue"></span>
            <br>
            <button class="copy-btn" onclick="copyToken()">Copy Token</button>
            <p style="font-size: 12px; margin-top: 10px; color: #666;">
                Copy this token and paste it in Discord using the "Enter hCaptcha Response" button.
            </p>
        </div>
    </div>

    <script>
        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const userId = urlParams.get('user');
        const guildId = urlParams.get('guild');
        const token = urlParams.get('token');
        
        // Display parameters
        document.getElementById('userId').textContent = userId || 'Not provided';
        document.getElementById('guildId').textContent = guildId || 'Not provided';
        document.getElementById('token').textContent = token || 'Not provided';
        
        let hcaptchaResponse = null;
        
        // hCaptcha callback
        function onCaptchaSuccess(response) {
            hcaptchaResponse = response;
            document.getElementById('submitBtn').disabled = false;
            document.getElementById('submitBtn').textContent = 'Complete Verification';
            
            // Show the response token for manual entry
            document.getElementById('tokenValue').textContent = response;
            document.getElementById('responseToken').style.display = 'block';
            
            showStatus('Captcha completed! You can now submit or copy the token for manual entry.', 'info');
        }
        
        async function submitVerification() {
            if (!hcaptchaResponse) {
                showStatus('Please complete the captcha first.', 'error');
                return;
            }

            showStatus('Submitting verification...', 'info');

            try {
                // Send verification request to backend
                const response = await fetch('/api/captcha/verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: parseInt(userId),
                        guild_id: parseInt(guildId),
                        verification_token: token,
                        hcaptcha_response: hcaptchaResponse
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showStatus(result.message || 'Verification completed! You can now return to Discord.', 'success');
                    document.getElementById('submitBtn').textContent = 'Verification Complete';
                    document.getElementById('submitBtn').disabled = true;
                } else {
                    showStatus(result.message || 'Verification failed. Please try again.', 'error');
                    // Reset captcha on failure
                    hcaptcha.reset();
                    hcaptchaResponse = null;
                    document.getElementById('submitBtn').disabled = true;
                    document.getElementById('submitBtn').textContent = 'Complete Verification';
                    document.getElementById('responseToken').style.display = 'none';
                }
            } catch (error) {
                console.error('Verification error:', error);
                showStatus('Network error. Please try again or use manual token entry.', 'error');
                // Reset captcha on error
                hcaptcha.reset();
                hcaptchaResponse = null;
                document.getElementById('submitBtn').disabled = true;
                document.getElementById('submitBtn').textContent = 'Complete Verification';
                document.getElementById('responseToken').style.display = 'none';
            }
        }
        
        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
        }
        
        function copyToken() {
            const tokenValue = document.getElementById('tokenValue').textContent;
            navigator.clipboard.writeText(tokenValue).then(() => {
                showStatus('Token copied to clipboard!', 'success');
            }).catch(() => {
                showStatus('Failed to copy token. Please select and copy manually.', 'error');
            });
        }
        
        // Check if required parameters are present
        if (!userId || !guildId || !token) {
            showStatus('Missing required parameters. Please use the link provided in Discord.', 'error');
        }
    </script>
</body>
</html>
