---
import Layout from '@/layouts/Layout.astro';
---

<Layout
    title="About OpenGuard"
    description="Learn about OpenGuard's mission, philosophy, and community-driven approach to moderation."
>
    <main class="container py-12">
        <div class="prose dark:prose-invert max-w-none">
            <h1>About OpenGuard</h1>
            <p>
                OpenGuard is a powerful, open-source Discord bot designed to provide
                top-tier security, moderation, and management for your community. Our
                mission is to offer a comprehensive, easy-to-use tool that helps
                server administrators maintain a safe and welcoming environment for
                their members.
            </p>
            <h2>Our Philosophy</h2>
            <p>
                We believe in the power of open-source software. By making our code
                publicly available, we foster a community of developers who can
                contribute to the project, ensuring that OpenGuard is always
                up-to-date with the latest features and security best practices.
                Transparency and collaboration are at the core of everything we do.
            </p>
            <h2>Features</h2>
            <ul>
                <li>
                    <strong>Advanced AI Moderation:</strong> Automatically detect and
                    act on harmful content, spam, and raids.
                </li>
                <li>
                    <strong>Comprehensive Logging:</strong> Keep detailed records of
                    all moderation actions and server events.
                </li>
                <li>
                    <strong>Customizable Settings:</strong> Tailor the bot's behavior
                    to fit the unique needs of your community.
                </li>
                <li>
                    <strong>User-Friendly Dashboard:</strong> Manage your server
                    settings and view analytics from a clean, intuitive web interface.
                </li>
            </ul>
        </div>
    </main>
</Layout>