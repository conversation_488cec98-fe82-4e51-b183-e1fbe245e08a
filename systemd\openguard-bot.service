[Unit]
Description=OpenGuard Discord Bot
After=network.target

[Service]
User=discordbot
Group=discordbot
WorkingDirectory=/home/<USER>/openguard

# Set up pyenv environment
Environment=PYENV_ROOT=/home/<USER>/.pyenv
Environment=PATH=/home/<USER>/.pyenv/versions/3.11.2/bin:/home/<USER>/.pyenv/bin:/usr/bin:/bin

# Use pyenv's Python
ExecStart=/home/<USER>/.pyenv/versions/3.11.2/bin/python -u bot.py

Restart=always
RestartSec=5s

[Install]
WantedBy=multi-user.target
