---
import Layout from '../layouts/Layout.astro';
import { Image } from 'astro:assets';
import openguardHero from 'public/openguard-hero.png';
---

<Layout
    title="OpenGuard - The Ultimate Guardian for Your Discord Server"
    description="OpenGuard is an open-source Discord bot offering AI moderation, customizable settings, and robust security features."
>
	<main>
		<section class="container grid grid-cols-1 items-center gap-12 py-24 md:grid-cols-2">
			<div class="flex flex-col gap-4">
				<h1 class="text-5xl font-bold">
					The Ultimate Guardian for Your Discord Server
				</h1>
				<p class="text-muted-foreground">
					OpenGuard is a powerful, open-source Discord bot designed to provide
					top-tier security, moderation, and management for your community.
				</p>
				<div class="flex gap-4">
					<a href="/dashboard/" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive bg-primary text-primary-foreground shadow-xs hover:bg-primary/90 h-9 px-4 py-2 has-[>svg]:px-3">
						Get Started
					</a>
					<a href="https://github.com/discordaimod/openguard" target="_blank" rel="noopener noreferrer" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-9 px-4 py-2 has-[>svg]:px-3">
						View on GitHub
					</a>
					<a href="https://discord.gg/bUZCj8cAmZ" target="_blank" rel="noopener noreferrer" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-9 px-4 py-2 has-[>svg]:px-3">
						Join Discord
					</a>
				</div>
			</div>
			<div class="flex justify-center items-center">
				<Image src={openguardHero} alt="OpenGuard illustration" class="max-w-full h-auto rounded-xl shadow-lg" />
			</div>
		</section>

		{/* Feature Section: AI Moderation */}
		<section class="container py-24">
			<div class="grid grid-cols-1 md:grid-cols-3 gap-8">
				<div class="flex flex-col gap-4 items-center bg-background rounded-xl shadow p-8">
					<h2 class="text-2xl font-bold text-center">
						Intelligent AI Moderation
					</h2>
					<p class="text-muted-foreground text-center">
						Leverage cutting-edge AI to automatically detect and handle spam,
						hate speech, and other undesirable content, keeping your community safe
						24/7.
					</p>
				</div>
				<div class="flex flex-col gap-4 items-center bg-background rounded-xl shadow p-8">
					<h2 class="text-2xl font-bold text-center">
						Fully Customizable Settings
					</h2>
					<p class="text-muted-foreground text-center">
						Tailor OpenGuard to your server's unique needs with an intuitive
						dashboard, allowing you to configure moderation rules, AI behavior,
						and more with ease.
					</p>
				</div>
				<div class="flex flex-col gap-4 items-center bg-background rounded-xl shadow p-8">
					<h2 class="text-2xl font-bold text-center">
						Open-Source & Community Driven
					</h2>
					<p class="text-muted-foreground text-center">
						OpenGuard is built with transparency in mind. Join our growing
						community, contribute to its development, and get support from
						fellow server administrators.
					</p>
				</div>
			</div>
		</section>
	</main>
</Layout>
