{"name": "website", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "test": "vitest run", "lint": "eslint ."}, "dependencies": {"@astrojs/cloudflare": "^12.6.0", "@astrojs/mdx": "^4.3.0", "@astrojs/react": "^4.3.0", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.11", "@types/canvas-confetti": "^1.9.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "astro": "^5.11.1", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/js": "^9.31.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jsdom": "^21.1.7", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "@vitejs/plugin-react": "^4.7.0", "eslint": "^9.31.0", "eslint-plugin-astro": "^1.3.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "jsdom": "^26.1.0", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3", "typescript-eslint": "^8.37.0", "vitest": "^3.2.4"}}