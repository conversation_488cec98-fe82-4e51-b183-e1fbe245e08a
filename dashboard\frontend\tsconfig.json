{"files": [], "references": [{"path": "./tsconfig.app.json"}], "compilerOptions": {"target": "ESNext", "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "types": ["jsdom"]}, "include": ["src", "vite.config.js", "lib"]}